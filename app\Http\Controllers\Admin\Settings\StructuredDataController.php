<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateStructuredDataRequest;

class StructuredDataController extends Controller
{
    public function index()
    {
        $settings = [
            'structured_data_enabled' => getSetting('structured_data_enabled', true),
            'structured_data_organization' => getSetting('structured_data_organization', true),
            'structured_data_website' => getSetting('structured_data_website', true),
            'structured_data_breadcrumbs' => getSetting('structured_data_breadcrumbs', true),
            'structured_data_articles' => getSetting('structured_data_articles', true),
            'organization_name' => getSetting('organization_name', getSetting('site_name')),
            'organization_logo' => getSetting('organization_logo', getSetting('logo')),
            'organization_url' => getSetting('organization_url', url('/')),
            'organization_description' => getSetting('organization_description', ''),
            'organization_contact_type' => getSetting('organization_contact_type', 'customer service'),
            'organization_telephone' => getSetting('organization_telephone', ''),
            'organization_email' => getSetting('organization_email', ''),
            'organization_address_street' => getSetting('organization_address_street', ''),
            'organization_address_city' => getSetting('organization_address_city', ''),
            'organization_address_region' => getSetting('organization_address_region', ''),
            'organization_address_postal' => getSetting('organization_address_postal', ''),
            'organization_address_country' => getSetting('organization_address_country', ''),
            'social_media_facebook' => getSetting('social_media_facebook', ''),
            'social_media_twitter' => getSetting('social_media_twitter', ''),
            'social_media_instagram' => getSetting('social_media_instagram', ''),
            'social_media_linkedin' => getSetting('social_media_linkedin', ''),
            'social_media_youtube' => getSetting('social_media_youtube', ''),
        ];

        return view('admin.settings.structured-data.index', compact('settings'));
    }

    public function update(UpdateStructuredDataRequest $request)
    {

        $settings = [
            'structured_data_enabled',
            'structured_data_organization',
            'structured_data_website',
            'structured_data_breadcrumbs',
            'structured_data_articles',
            'organization_name',
            'organization_logo',
            'organization_url',
            'organization_description',
            'organization_contact_type',
            'organization_telephone',
            'organization_email',
            'organization_address_street',
            'organization_address_city',
            'organization_address_region',
            'organization_address_postal',
            'organization_address_country',
            'social_media_facebook',
            'social_media_twitter',
            'social_media_instagram',
            'social_media_linkedin',
            'social_media_youtube',
        ];

        foreach ($settings as $setting) {
            setSetting($setting, $request->$setting);
        }

        showToastr(__('lobage.toastr.update'));
        return back();
    }

    public function preview()
    {
        $structuredData = $this->generateStructuredData();
        return response()->json($structuredData, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }

    private function generateStructuredData()
    {
        $data = [];

        if (getSetting('structured_data_organization', true)) {
            $data[] = $this->getOrganizationSchema();
        }

        if (getSetting('structured_data_website', true)) {
            $data[] = $this->getWebsiteSchema();
        }

        return $data;
    }

    private function getOrganizationSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => getSetting('organization_name', getSetting('site_name')),
            'url' => getSetting('organization_url', url('/')),
        ];

        if ($logo = getSetting('organization_logo')) {
            $schema['logo'] = asset($logo);
        }

        if ($description = getSetting('organization_description')) {
            $schema['description'] = $description;
        }

        // Contact information
        $contactPoint = [];
        if ($telephone = getSetting('organization_telephone')) {
            $contactPoint['telephone'] = $telephone;
        }
        if ($email = getSetting('organization_email')) {
            $contactPoint['email'] = $email;
        }
        if ($contactType = getSetting('organization_contact_type')) {
            $contactPoint['contactType'] = $contactType;
        }

        if (!empty($contactPoint)) {
            $contactPoint['@type'] = 'ContactPoint';
            $schema['contactPoint'] = $contactPoint;
        }

        // Address
        $address = [];
        if ($street = getSetting('organization_address_street')) {
            $address['streetAddress'] = $street;
        }
        if ($city = getSetting('organization_address_city')) {
            $address['addressLocality'] = $city;
        }
        if ($region = getSetting('organization_address_region')) {
            $address['addressRegion'] = $region;
        }
        if ($postal = getSetting('organization_address_postal')) {
            $address['postalCode'] = $postal;
        }
        if ($country = getSetting('organization_address_country')) {
            $address['addressCountry'] = $country;
        }

        if (!empty($address)) {
            $address['@type'] = 'PostalAddress';
            $schema['address'] = $address;
        }

        // Social media
        $socialMedia = [];
        if ($facebook = getSetting('social_media_facebook')) {
            $socialMedia[] = $facebook;
        }
        if ($twitter = getSetting('social_media_twitter')) {
            $socialMedia[] = $twitter;
        }
        if ($instagram = getSetting('social_media_instagram')) {
            $socialMedia[] = $instagram;
        }
        if ($linkedin = getSetting('social_media_linkedin')) {
            $socialMedia[] = $linkedin;
        }
        if ($youtube = getSetting('social_media_youtube')) {
            $socialMedia[] = $youtube;
        }

        if (!empty($socialMedia)) {
            $schema['sameAs'] = $socialMedia;
        }

        return $schema;
    }

    private function getWebsiteSchema()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => getSetting('site_name'),
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/') . '?search={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }
}
