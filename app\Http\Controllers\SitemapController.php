<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class SitemapController extends Controller
{
    public function index()
    {
        try {
            if (function_exists('getSetting') && !getSetting('sitemap_enabled', true)) {
                abort(404);
            }
        } catch (\Exception $e) {
            // If getSetting fails, continue with sitemap generation
        }

        // Check if database is available
        $databaseAvailable = $this->isDatabaseAvailable();

        $cacheKey = 'sitemap_xml';
        $cacheDuration = function_exists('getSetting') ? getSetting('sitemap_cache_duration', 60) : 60;

        if ($databaseAvailable) {
            $sitemap = Cache::remember($cacheKey, now()->addMinutes($cacheDuration), function () {
                return $this->generateSitemap();
            });
            Cache::put('sitemap_last_generated', now());
        } else {
            // Generate basic sitemap without database
            $sitemap = $this->generateBasicSitemap();
        }

        return Response::make($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=' . ($cacheDuration * 60),
        ]);
    }

    private function generateSitemap()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . PHP_EOL;

        // Add homepage
        $xml .= $this->addUrl(url('/'), now(), 'daily', '1.0');

        // Add static pages
        if (!function_exists('getSetting') || getSetting('sitemap_include_pages', true)) {
            $xml .= $this->addPages();
        }

        // Add blog posts
        if ((!function_exists('getSetting') || getSetting('sitemap_include_blog', true)) &&
            (!function_exists('getSetting') || getSetting('enable_blog', true))) {
            $xml .= $this->addBlogPosts();
            $xml .= $this->addBlogIndex();
        }

        $xml .= '</urlset>';

        return $xml;
    }

    private function addUrl($url, $lastmod = null, $changefreq = 'weekly', $priority = '0.5', $alternates = [])
    {
        $xml = '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($url) . '</loc>' . PHP_EOL;

        if ($lastmod) {
            $xml .= '    <lastmod>' . $lastmod->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        }

        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . $priority . '</priority>' . PHP_EOL;

        // Add hreflang alternates
        foreach ($alternates as $lang => $altUrl) {
            $xml .= '    <xhtml:link rel="alternate" hreflang="' . $lang . '" href="' . htmlspecialchars($altUrl) . '" />' . PHP_EOL;
        }

        $xml .= '  </url>' . PHP_EOL;

        return $xml;
    }

    private function addPages()
    {
        $xml = '';
        try {
            $pages = Page::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_pages_priority', '0.8') : '0.8';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_pages_changefreq', 'weekly') : 'weekly';
        } catch (\Exception $e) {
            return '';
        }

        foreach ($pages as $page) {
            $url = route('page', $page->slug);
            $alternates = $this->getPageAlternates($page);
            $xml .= $this->addUrl($url, $page->updated_at, $changefreq, $priority, $alternates);
        }

        return $xml;
    }

    private function addBlogPosts()
    {
        $xml = '';
        try {
            $posts = BlogPost::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';
        } catch (\Exception $e) {
            return '';
        }

        foreach ($posts as $post) {
            $url = route('posts', $post->slug);
            $alternates = $this->getBlogPostAlternates($post);
            $xml .= $this->addUrl($url, $post->updated_at, $changefreq, $priority, $alternates);
        }

        return $xml;
    }

    private function addBlogIndex()
    {
        $xml = '';
        try {
            $url = route('blog');
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';
            $lastPost = BlogPost::where('status', 1)->latest('updated_at')->first();
            $lastmod = $lastPost ? $lastPost->updated_at : now();
        } catch (\Exception $e) {
            return '';
        }

        $alternates = $this->getBlogIndexAlternates();
        $xml .= $this->addUrl($url, $lastmod, $changefreq, $priority, $alternates);

        return $xml;
    }

    private function getPageAlternates($page)
    {
        $alternates = [];
        $languages = Language::where('status', 1)->get();

        foreach ($languages as $language) {
            $altPage = Page::where('slug', $page->slug)
                ->where('lang', $language->code)
                ->where('status', 1)
                ->first();

            if ($altPage) {
                $altUrl = str_replace('/' . getCurrentLang() . '/', '/' . $language->code . '/', route('page', $altPage->slug));
                $alternates[$language->code] = $altUrl;
            }
        }

        return $alternates;
    }

    private function getBlogPostAlternates($post)
    {
        $alternates = [];
        $languages = Language::where('status', 1)->get();

        foreach ($languages as $language) {
            $altPost = BlogPost::where('slug', $post->slug)
                ->where('lang', $language->code)
                ->where('status', 1)
                ->first();

            if ($altPost) {
                $altUrl = str_replace('/' . getCurrentLang() . '/', '/' . $language->code . '/', route('posts', $altPost->slug));
                $alternates[$language->code] = $altUrl;
            }
        }

        return $alternates;
    }

    private function getBlogIndexAlternates()
    {
        $alternates = [];
        $languages = Language::where('status', 1)->get();

        foreach ($languages as $language) {
            $altUrl = str_replace('/' . getCurrentLang() . '/', '/' . $language->code . '/', route('blog'));
            $alternates[$language->code] = $altUrl;
        }

        return $alternates;
    }

    private function isDatabaseAvailable()
    {
        try {
            \DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function generateBasicSitemap()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

        // Add homepage
        $xml .= '<url>' . PHP_EOL;
        $xml .= '<loc>' . url('/') . '</loc>' . PHP_EOL;
        $xml .= '<lastmod>' . now()->toISOString() . '</lastmod>' . PHP_EOL;
        $xml .= '<changefreq>daily</changefreq>' . PHP_EOL;
        $xml .= '<priority>1.0</priority>' . PHP_EOL;
        $xml .= '</url>' . PHP_EOL;

        $xml .= '</urlset>';

        return $xml;
    }
}
