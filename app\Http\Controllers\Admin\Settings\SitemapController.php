<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Models\Page;
use App\Models\Setting;
use App\Models\BlogPost;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSitemapRequest;
use Illuminate\Support\Facades\Cache;

class SitemapController extends Controller
{
    public function index()
    {
        try {
            $settings = [
                'sitemap_enabled' => getSetting('sitemap_enabled', true),
                'sitemap_include_pages' => getSetting('sitemap_include_pages', true),
                'sitemap_include_blog' => getSetting('sitemap_include_blog', true),
                'sitemap_pages_priority' => getSetting('sitemap_pages_priority', '0.8'),
                'sitemap_blog_priority' => getSetting('sitemap_blog_priority', '0.7'),
                'sitemap_pages_changefreq' => getSetting('sitemap_pages_changefreq', 'weekly'),
                'sitemap_blog_changefreq' => getSetting('sitemap_blog_changefreq', 'weekly'),
                'sitemap_cache_duration' => getSetting('sitemap_cache_duration', 60),
            ];

            $stats = [
                'total_pages' => Page::where('status', 1)->count(),
                'total_blog_posts' => BlogPost::where('status', 1)->count(),
                'last_generated' => Cache::get('sitemap_last_generated'),
            ];
        } catch (\Exception $e) {
            // If database is not available, use default values
            $settings = [
                'sitemap_enabled' => true,
                'sitemap_include_pages' => true,
                'sitemap_include_blog' => true,
                'sitemap_pages_priority' => '0.8',
                'sitemap_blog_priority' => '0.7',
                'sitemap_pages_changefreq' => 'weekly',
                'sitemap_blog_changefreq' => 'weekly',
                'sitemap_cache_duration' => 60,
            ];

            $stats = [
                'total_pages' => 0,
                'total_blog_posts' => 0,
                'last_generated' => null,
            ];
        }

        return view('admin.settings.sitemap.index', compact('settings', 'stats'));
    }

    public function update(UpdateSitemapRequest $request)
    {
        try {
            $settings = [
                'sitemap_enabled',
                'sitemap_include_pages',
                'sitemap_include_blog',
                'sitemap_pages_priority',
                'sitemap_blog_priority',
                'sitemap_pages_changefreq',
                'sitemap_blog_changefreq',
                'sitemap_cache_duration',
            ];

            foreach ($settings as $setting) {
                setSetting($setting, $request->$setting);
            }

            // Clear sitemap cache
            Cache::forget('sitemap_xml');
            Cache::forget('sitemap_last_generated');

            showToastr(__('lobage.toastr.update'));
        } catch (\Exception $e) {
            showToastr(__('Failed to update sitemap settings: ') . $e->getMessage(), 'error');
        }

        return back();
    }

    public function generate()
    {
        try {
            // Clear existing cache
            Cache::forget('sitemap_xml');
            Cache::forget('sitemap_last_generated');

            // Generate new sitemap (this will be cached)
            $sitemapController = new \App\Http\Controllers\SitemapController();
            $sitemapController->index();

            showToastr(__('Sitemap generated successfully'));
        } catch (\Exception $e) {
            showToastr(__('Failed to generate sitemap: ') . $e->getMessage(), 'error');
        }

        return back();
    }

    public function preview()
    {
        $sitemapController = new \App\Http\Controllers\SitemapController();
        return $sitemapController->index();
    }
}