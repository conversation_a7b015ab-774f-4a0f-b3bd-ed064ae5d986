<?php

namespace App\Services;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\BlogCategory;

class StructuredDataService
{
    private function safeSetting($key, $default = null)
    {
        try {
            return function_exists('getSetting') ? getSetting($key, $default) : $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
    public function generateStructuredData($type = 'general', $data = null)
    {
        try {
            if (!$this->safeSetting('structured_data_enabled', true)) {
                return '';
            }

            $schemas = [];

            switch ($type) {
                case 'general':
                    $schemas = $this->getGeneralSchemas();
                    break;
                case 'article':
                    $schemas = $this->getArticleSchema($data);
                    break;
                case 'breadcrumbs':
                    $schemas = $this->getBreadcrumbSchema($data);
                    break;
                case 'page':
                    $schemas = $this->getPageSchema($data);
                    break;
            }

            if (empty($schemas)) {
                return '';
            }

            return $this->renderJsonLd($schemas);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('StructuredDataService Error: ' . $e->getMessage());
            return '';
        }
    }

    private function getGeneralSchemas()
    {
        $schemas = [];

        try {
            if ($this->safeSetting('structured_data_organization', true)) {
                $schemas[] = $this->getOrganizationSchema();
            }

            if ($this->safeSetting('structured_data_website', true)) {
                $schemas[] = $this->getWebsiteSchema();
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('General Schemas Error: ' . $e->getMessage());
        }

        return $schemas;
    }

    private function getOrganizationSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            'url' => $this->safeSetting('organization_url', url('/')),
        ];

        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['logo'] = asset($logo);
        }

        if ($description = $this->safeSetting('organization_description')) {
            $schema['description'] = $description;
        }

        // Contact information
        $contactPoint = [];
        if ($telephone = $this->safeSetting('organization_telephone')) {
            $contactPoint['telephone'] = $telephone;
        }
        if ($email = $this->safeSetting('organization_email')) {
            $contactPoint['email'] = $email;
        }
        if ($contactType = $this->safeSetting('organization_contact_type')) {
            $contactPoint['contactType'] = $contactType;
        }

        if (!empty($contactPoint)) {
            $contactPoint['@type'] = 'ContactPoint';
            $schema['contactPoint'] = $contactPoint;
        }

        // Address
        $address = [];
        if ($street = $this->safeSetting('organization_address_street')) {
            $address['streetAddress'] = $street;
        }
        if ($city = $this->safeSetting('organization_address_city')) {
            $address['addressLocality'] = $city;
        }
        if ($region = $this->safeSetting('organization_address_region')) {
            $address['addressRegion'] = $region;
        }
        if ($postal = $this->safeSetting('organization_address_postal')) {
            $address['postalCode'] = $postal;
        }
        if ($country = $this->safeSetting('organization_address_country')) {
            $address['addressCountry'] = $country;
        }

        if (!empty($address)) {
            $address['@type'] = 'PostalAddress';
            $schema['address'] = $address;
        }

        // Social media
        $socialMedia = [];
        if ($facebook = $this->safeSetting('social_media_facebook')) {
            $socialMedia[] = $facebook;
        }
        if ($twitter = $this->safeSetting('social_media_twitter')) {
            $socialMedia[] = $twitter;
        }
        if ($instagram = $this->safeSetting('social_media_instagram')) {
            $socialMedia[] = $instagram;
        }
        if ($linkedin = $this->safeSetting('social_media_linkedin')) {
            $socialMedia[] = $linkedin;
        }
        if ($youtube = $this->safeSetting('social_media_youtube')) {
            $socialMedia[] = $youtube;
        }

        if (!empty($socialMedia)) {
            $schema['sameAs'] = $socialMedia;
        }

        return $schema;
    }

    private function getWebsiteSchema()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->safeSetting('site_name', 'Website'),
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/') . '?search={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }

    private function getArticleSchema($post)
    {
        if (!$this->safeSetting('structured_data_articles', true) || !$post) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $post->title,
            'description' => $post->meta_description ?: $post->description,
            'url' => route('posts', $post->slug),
            'datePublished' => $post->created_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'author' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website'))
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            ]
        ];

        if ($post->image) {
            $schema['image'] = asset($post->image);
        }

        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['publisher']['logo'] = [
                '@type' => 'ImageObject',
                'url' => asset($logo)
            ];
        }

        if ($post->category) {
            $schema['articleSection'] = $post->category->name;
        }

        if ($post->tags) {
            $tags = explode(',', $post->tags);
            $schema['keywords'] = array_map('trim', $tags);
        }

        return [$schema];
    }

    private function getPageSchema($page)
    {
        if (!$page) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $page->title,
            'description' => $page->meta_description,
            'url' => route('page', $page->slug),
            'datePublished' => $page->created_at->toISOString(),
            'dateModified' => $page->updated_at->toISOString(),
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => $this->safeSetting('site_name', 'Website'),
                'url' => url('/')
            ]
        ];

        return [$schema];
    }

    private function getBreadcrumbSchema($breadcrumbs)
    {
        if (!$this->safeSetting('structured_data_breadcrumbs', true) || empty($breadcrumbs)) {
            return [];
        }

        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [[
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ]];
    }

    private function renderJsonLd($schemas)
    {
        if (empty($schemas)) {
            return '';
        }

        $output = '';
        foreach ($schemas as $schema) {
            $output .= '<script type="application/ld+json">' . PHP_EOL;
            $output .= json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $output .= '</script>' . PHP_EOL;
        }

        return $output;
    }

    public function getBreadcrumbs($currentPage = null, $category = null)
    {
        $breadcrumbs = [
            [
                'name' => __('Home'),
                'url' => url('/')
            ]
        ];

        if ($category) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
            $breadcrumbs[] = [
                'name' => $category->name,
                'url' => route('category', $category->slug)
            ];
        } elseif (request()->is('*/blog*')) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
        }

        if ($currentPage) {
            $breadcrumbs[] = [
                'name' => $currentPage,
                'url' => url()->current()
            ];
        }

        return $breadcrumbs;
    }
}
